import { Router } from 'express';
import { MaterialController } from '../controllers/materialController';
import { authenticate } from '../middleware/auth';
import { requireTeacher, requireTeacherOrHODOrSuperAdmin } from '../middleware/rbac';

const router = Router();

/**
 * Material Routes
 * Base path: /materials
 */

// All routes require authentication
router.use(authenticate);

// Create material metadata (Teachers only)
router.post('/', requireTeacher, MaterialController.createMaterial);

// Trigger material ingestion (Teachers only)
router.post('/:id/ingest', requireTeacher, MaterialController.ingestMaterial);

// Get materials by subject (All authenticated users)
router.get('/', MaterialController.getMaterials);

// Get single material by ID (All authenticated users)
router.get('/:id', MaterialController.getMaterialById);

// Generate download URL (All authenticated users)
router.get('/:id/download', MaterialController.getDownloadUrl);

// Delete material (Teachers only - own materials)
router.delete('/:id', requireTeacher, MaterialController.deleteMaterial);

export default router;
