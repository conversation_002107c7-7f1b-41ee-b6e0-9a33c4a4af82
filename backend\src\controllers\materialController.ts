import { Request, Response } from 'express';
import { z } from 'zod';
import { MaterialService } from '../services/materialService';
import { AuthenticatedRequest, ApiResponse } from '../types';

// Validation schemas
const createMaterialSchema = z.object({
  subjectId: z.string().min(1, 'Subject ID is required'),
  title: z.string().min(1, 'Title is required'),
  fileName: z.string().min(1, 'File name is required'),
  fileSize: z.number().positive('File size must be positive'),
  mimeType: z.string().min(1, 'MIME type is required'),
});

const getMaterialsSchema = z.object({
  subjectId: z.string().optional(),
});

export class MaterialController {
  /**
   * Create new material (metadata only)
   * POST /materials
   * Frontend: Material upload form (step 1 - metadata)
   */
  static async createMaterial(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = createMaterialSchema.parse(req.body);
      
      const result = await MaterialService.createMaterial({
        ...validatedData,
        teacherId: req.user.id,
      });
      
      res.status(201).json({
        success: true,
        data: result,
        message: 'Material created successfully. Use the upload URL to upload the file.',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to create material';
      const statusCode = errorMessage.includes('not found') || errorMessage.includes('access denied') ? 403 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Trigger material ingestion (chunking and embedding)
   * POST /materials/:id/ingest
   * Frontend: After successful file upload to S3
   */
  static async ingestMaterial(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const materialId = req.params.id;
      
      const material = await MaterialService.ingestMaterial(materialId);
      
      res.json({
        success: true,
        data: material,
        message: 'Material ingestion started. Processing will complete in the background.',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start material ingestion';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get materials by subject
   * GET /materials?subjectId=...
   * Frontend: Subject materials list, teacher dashboard
   */
  static async getMaterials(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const { subjectId } = getMaterialsSchema.parse(req.query);
      
      if (!subjectId) {
        res.status(400).json({
          success: false,
          error: 'Subject ID is required',
        });
        return;
      }

      // Teachers can only see their own materials, students can see all materials for subjects they have access to
      const teacherId = req.user.role === 'teacher' ? req.user.id : undefined;
      
      const materials = await MaterialService.getMaterialsBySubject(subjectId, teacherId);
      
      res.json({
        success: true,
        data: materials,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to get materials';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get single material by ID
   * GET /materials/:id
   * Frontend: Material details page
   */
  static async getMaterialById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const materialId = req.params.id;
      
      // Teachers can only see their own materials
      const teacherId = req.user.role === 'teacher' ? req.user.id : undefined;
      
      const material = await MaterialService.getMaterialById(materialId, teacherId);
      
      res.json({
        success: true,
        data: material,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get material';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Delete material
   * DELETE /materials/:id
   * Frontend: Material management page
   */
  static async deleteMaterial(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const materialId = req.params.id;
      
      await MaterialService.deleteMaterial(materialId, req.user.id);
      
      res.json({
        success: true,
        message: 'Material deleted successfully',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete material';
      const statusCode = errorMessage.includes('not found') || errorMessage.includes('access denied') ? 403 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Generate download URL for material
   * GET /materials/:id/download
   * Frontend: Download button in material list
   */
  static async getDownloadUrl(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const materialId = req.params.id;
      
      const material = await MaterialService.getMaterialById(materialId);
      const downloadUrl = await MaterialService.generateDownloadUrl(material.s3Key);
      
      res.json({
        success: true,
        data: { downloadUrl },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate download URL';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }
}
