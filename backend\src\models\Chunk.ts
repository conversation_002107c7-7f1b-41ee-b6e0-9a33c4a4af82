import mongoose, { Schema } from 'mongoose';
import { IChunk } from '../types';

const chunkSchema = new Schema<IChunk>({
  namespace: {
    type: String,
    required: true,
    index: true,
  },
  text: {
    type: String,
    required: true,
  },
  embedding: {
    type: [Number],
    required: true,
  },
  metadata: {
    materialId: {
      type: Schema.Types.ObjectId,
      ref: 'Material',
      required: true,
    },
    subjectId: {
      type: Schema.Types.ObjectId,
      ref: 'Subject',
      required: true,
    },
    teacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    chunkIndex: {
      type: Number,
      required: true,
    },
  },
}, {
  timestamps: true,
});

// Indexes for efficient vector search and queries
chunkSchema.index({ namespace: 1 });
chunkSchema.index({ 'metadata.subjectId': 1 });
chunkSchema.index({ 'metadata.materialId': 1 });
chunkSchema.index({ 'metadata.teacherId': 1 });

// Vector search index (for MongoDB Atlas Vector Search)
// This would be created via MongoDB Atlas UI or mongosh
// chunkSchema.index({ embedding: "vectorSearch" });

export const Chunk = mongoose.model<IChunk>('Chunk', chunkSchema);
export default Chunk;
