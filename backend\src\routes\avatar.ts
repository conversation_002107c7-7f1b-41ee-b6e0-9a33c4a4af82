import { Router } from 'express';
import { AvatarController } from '../controllers/avatarController';
import { authenticate } from '../middleware/auth';
import { requireTeacher } from '../middleware/rbac';

const router = Router();

/**
 * Avatar Routes
 * Base path: /avatar
 */

// All routes require authentication
router.use(authenticate);

// Create or update avatar (Teachers only)
router.post('/', requireTeacher, AvatarController.createOrUpdateAvatar);

// Get my avatars (Teachers only)
router.get('/my', requireTeacher, AvatarController.getMyAvatars);

// Get avatar by teacher ID (All authenticated users)
router.get('/:teacherId', AvatarController.getAvatarByTeacher);

// Update avatar (Teachers only - own avatars)
router.put('/:id', requireTeacher, AvatarController.updateAvatar);

// Delete avatar (Teachers only - own avatars)
router.delete('/:id', requireTeacher, AvatarController.deleteAvatar);

export default router;
