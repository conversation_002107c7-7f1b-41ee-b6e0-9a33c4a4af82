import mongoose, { Schema } from 'mongoose';
import { IAnnouncement, UserRole } from '../types';

const announcementSchema = new Schema<IAnnouncement>({
  orgId: {
    type: String,
    required: true,
  },
  authorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  audience: [{
    type: String,
    enum: Object.values(UserRole),
    required: true,
  }],
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  body: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
announcementSchema.index({ orgId: 1 });
announcementSchema.index({ authorId: 1 });
announcementSchema.index({ orgId: 1, isActive: 1, createdAt: -1 });
announcementSchema.index({ audience: 1, isActive: 1, createdAt: -1 });

export const Announcement = mongoose.model<IAnnouncement>('Announcement', announcementSchema);
export default Announcement;
