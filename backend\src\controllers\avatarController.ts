import { Request, Response } from 'express';
import { z } from 'zod';
import { Avatar, Subject } from '../models';
import { AuthenticatedRequest, ApiResponse } from '../types';

// Validation schemas
const createAvatarSchema = z.object({
  subjectId: z.string().min(1, 'Subject ID is required'),
  style: z.enum(['professional', 'friendly', 'casual', 'formal']),
  voice: z.enum(['male', 'female', 'neutral']),
  persona: z.string().max(1000, 'Persona must be less than 1000 characters').optional(),
});

const updateAvatarSchema = z.object({
  style: z.enum(['professional', 'friendly', 'casual', 'formal']).optional(),
  voice: z.enum(['male', 'female', 'neutral']).optional(),
  persona: z.string().max(1000, 'Persona must be less than 1000 characters').optional(),
});

export class AvatarController {
  /**
   * Create or update avatar for a subject
   * POST /avatar
   * Frontend: Avatar creation/customization page
   */
  static async createOrUpdateAvatar(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can create avatars',
        });
        return;
      }

      const validatedData = createAvatarSchema.parse(req.body);
      
      // Verify teacher owns the subject
      const subject = await Subject.findOne({
        _id: validatedData.subjectId,
        teacherId: req.user.id,
      });

      if (!subject) {
        res.status(403).json({
          success: false,
          error: 'Subject not found or access denied',
        });
        return;
      }

      // Check if avatar already exists
      let avatar = await Avatar.findOne({
        teacherId: req.user.id,
        subjectId: validatedData.subjectId,
      });

      if (avatar) {
        // Update existing avatar
        avatar.style = validatedData.style;
        avatar.voice = validatedData.voice;
        if (validatedData.persona !== undefined) {
          avatar.persona = validatedData.persona;
        }
        await avatar.save();
      } else {
        // Create new avatar
        avatar = new Avatar({
          teacherId: req.user.id,
          subjectId: validatedData.subjectId,
          style: validatedData.style,
          voice: validatedData.voice,
          persona: validatedData.persona,
        });
        await avatar.save();
      }

      await avatar.populate('subjectId', 'name');
      
      res.status(avatar.isNew ? 201 : 200).json({
        success: true,
        data: avatar,
        message: `Avatar ${avatar.isNew ? 'created' : 'updated'} successfully`,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to create/update avatar';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get avatar by teacher ID
   * GET /avatar/:teacherId
   * Frontend: Student chat interface, avatar display
   */
  static async getAvatarByTeacher(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const teacherId = req.params.teacherId;
      const subjectId = req.query.subjectId as string;

      if (!subjectId) {
        res.status(400).json({
          success: false,
          error: 'Subject ID is required',
        });
        return;
      }

      const avatar = await Avatar.findOne({
        teacherId,
        subjectId,
      }).populate('subjectId', 'name');

      if (!avatar) {
        res.status(404).json({
          success: false,
          error: 'Avatar not found',
        });
        return;
      }

      res.json({
        success: true,
        data: avatar,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get avatar';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get all avatars for current teacher
   * GET /avatar/my
   * Frontend: Teacher dashboard, avatar management
   */
  static async getMyAvatars(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can view their avatars',
        });
        return;
      }

      const avatars = await Avatar.find({
        teacherId: req.user.id,
      }).populate('subjectId', 'name');

      res.json({
        success: true,
        data: avatars,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get avatars';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Update avatar
   * PUT /avatar/:id
   * Frontend: Avatar editing page
   */
  static async updateAvatar(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can update avatars',
        });
        return;
      }

      const avatarId = req.params.id;
      const validatedData = updateAvatarSchema.parse(req.body);

      const avatar = await Avatar.findOne({
        _id: avatarId,
        teacherId: req.user.id,
      });

      if (!avatar) {
        res.status(404).json({
          success: false,
          error: 'Avatar not found or access denied',
        });
        return;
      }

      // Update fields
      if (validatedData.style) avatar.style = validatedData.style;
      if (validatedData.voice) avatar.voice = validatedData.voice;
      if (validatedData.persona !== undefined) avatar.persona = validatedData.persona;

      await avatar.save();
      await avatar.populate('subjectId', 'name');

      res.json({
        success: true,
        data: avatar,
        message: 'Avatar updated successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to update avatar';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Delete avatar
   * DELETE /avatar/:id
   * Frontend: Avatar management page
   */
  static async deleteAvatar(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can delete avatars',
        });
        return;
      }

      const avatarId = req.params.id;

      const avatar = await Avatar.findOneAndDelete({
        _id: avatarId,
        teacherId: req.user.id,
      });

      if (!avatar) {
        res.status(404).json({
          success: false,
          error: 'Avatar not found or access denied',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Avatar deleted successfully',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete avatar';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }
}
