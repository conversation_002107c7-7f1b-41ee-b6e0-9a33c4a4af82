'use client';

// Teacher Avatar Setup Page - Customize AI teacher appearance and personality

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  ArrowLeft, 
  Save, 
  Play, 
  Palette,
  Volume2,
  User,
  Eye
} from 'lucide-react';
import Experience from '@/components/3d/Experience';
import { 
  useAvatarConfig, 
  useAvatarHelpers,
  useAITeacherActions,
  defaultAvatarConfig 
} from '@/store/useAITeacher';

export default function TeacherAvatarPage() {
  const router = useRouter();
  const avatarConfig = useAvatarConfig();
  const { updateAvatarAppearance, updateVoiceSettings, updatePersonality } = useAvatarHelpers();
  const { setAvatar, setBlackboardText } = useAITeacherActions();
  const [isSaving, setIsSaving] = useState(false);
  const [previewText, setPreviewText] = useState('Hello! I am your AI teacher. How can I help you today?');

  // Initialize avatar config if not set
  const currentConfig = avatarConfig || {
    ...defaultAvatarConfig,
    id: 'temp',
    teacherId: 'current-teacher',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const handleSaveAvatar = async () => {
    setIsSaving(true);
    try {
      // TODO: Save to backend when API is ready
      console.log('Saving avatar configuration:', currentConfig);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Avatar configuration saved successfully!');
    } catch (error) {
      console.error('Failed to save avatar:', error);
      alert('Failed to save avatar configuration. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePreviewVoice = () => {
    // TODO: Implement voice preview when TTS API is ready
    console.log('Playing voice preview with settings:', currentConfig.voiceSettings);
    setBlackboardText(previewText);
  };

  const handleResetToDefault = () => {
    setAvatar({
      ...defaultAvatarConfig,
      id: currentConfig.id,
      teacherId: currentConfig.teacherId,
      createdAt: currentConfig.createdAt,
      updatedAt: new Date(),
    });
  };

  return (
    <div className="h-[calc(100vh-4rem)] flex">
      {/* Left Panel - 3D Preview */}
      <div className="w-1/2 bg-gray-100 relative">
        <div className="absolute top-4 left-4 z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/teacher')}
            className="bg-white/90 backdrop-blur-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
        
        <div className="absolute top-4 right-4 z-10">
          <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
            3D Preview
          </Badge>
        </div>
        
        <Experience 
          enableControls={true}
          showBlackboard={true}
          className="w-full h-full"
        />
        
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <Card className="bg-white/90 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Button
                  onClick={handlePreviewVoice}
                  size="sm"
                  className="flex-shrink-0"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Preview Voice
                </Button>
                <input
                  type="text"
                  value={previewText}
                  onChange={(e) => setPreviewText(e.target.value)}
                  placeholder="Enter text to preview..."
                  className="flex-1 px-3 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Right Panel - Configuration */}
      <div className="w-1/2 overflow-y-auto bg-white">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold flex items-center space-x-2">
                <Bot className="h-6 w-6 text-blue-600" />
                <span>AI Avatar Setup</span>
              </h1>
              <p className="text-gray-600 mt-1">
                Customize your AI teaching assistant's appearance and personality
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleResetToDefault}
                size="sm"
              >
                Reset to Default
              </Button>
              <Button
                onClick={handleSaveAvatar}
                disabled={isSaving}
                size="sm"
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Avatar
                  </>
                )}
              </Button>
            </div>
          </div>

          <Tabs defaultValue="appearance" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="appearance" className="flex items-center space-x-2">
                <Palette className="h-4 w-4" />
                <span>Appearance</span>
              </TabsTrigger>
              <TabsTrigger value="voice" className="flex items-center space-x-2">
                <Volume2 className="h-4 w-4" />
                <span>Voice</span>
              </TabsTrigger>
              <TabsTrigger value="personality" className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Personality</span>
              </TabsTrigger>
            </TabsList>

            {/* Appearance Tab */}
            <TabsContent value="appearance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Physical Appearance</CardTitle>
                  <CardDescription>
                    Customize the visual appearance of your AI avatar
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="skinTone">Skin Tone</Label>
                    <Select
                      value={currentConfig.appearance.skinTone}
                      onValueChange={(value) => updateAvatarAppearance({ skinTone: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="olive">Olive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="hairColor">Hair Color</Label>
                    <Select
                      value={currentConfig.appearance.hairColor}
                      onValueChange={(value) => updateAvatarAppearance({ hairColor: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="black">Black</SelectItem>
                        <SelectItem value="brown">Brown</SelectItem>
                        <SelectItem value="blonde">Blonde</SelectItem>
                        <SelectItem value="red">Red</SelectItem>
                        <SelectItem value="gray">Gray</SelectItem>
                        <SelectItem value="white">White</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="eyeColor">Eye Color</Label>
                    <Select
                      value={currentConfig.appearance.eyeColor}
                      onValueChange={(value) => updateAvatarAppearance({ eyeColor: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="brown">Brown</SelectItem>
                        <SelectItem value="blue">Blue</SelectItem>
                        <SelectItem value="green">Green</SelectItem>
                        <SelectItem value="hazel">Hazel</SelectItem>
                        <SelectItem value="gray">Gray</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="clothing">Clothing Style</Label>
                    <Select
                      value={currentConfig.appearance.clothing}
                      onValueChange={(value) => updateAvatarAppearance({ clothing: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="casual">Casual</SelectItem>
                        <SelectItem value="formal">Formal</SelectItem>
                        <SelectItem value="academic">Academic</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Voice Tab */}
            <TabsContent value="voice" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Voice Settings</CardTitle>
                  <CardDescription>
                    Adjust the voice characteristics of your AI avatar
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label>Pitch: {currentConfig.voiceSettings.pitch.toFixed(1)}</Label>
                    <Slider
                      value={[currentConfig.voiceSettings.pitch]}
                      onValueChange={([value]) => updateVoiceSettings({ pitch: value })}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Speed: {currentConfig.voiceSettings.speed.toFixed(1)}</Label>
                    <Slider
                      value={[currentConfig.voiceSettings.speed]}
                      onValueChange={([value]) => updateVoiceSettings({ speed: value })}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Volume: {Math.round(currentConfig.voiceSettings.volume * 100)}%</Label>
                    <Slider
                      value={[currentConfig.voiceSettings.volume]}
                      onValueChange={([value]) => updateVoiceSettings({ volume: value })}
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>

                  <Button
                    onClick={handlePreviewVoice}
                    className="w-full"
                    variant="outline"
                  >
                    <Volume2 className="h-4 w-4 mr-2" />
                    Test Voice Settings
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Personality Tab */}
            <TabsContent value="personality" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personality Traits</CardTitle>
                  <CardDescription>
                    Define how your AI avatar interacts with students
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="tone">Communication Tone</Label>
                    <Select
                      value={currentConfig.personality.tone}
                      onValueChange={(value: any) => updatePersonality({ tone: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="formal">Formal</SelectItem>
                        <SelectItem value="casual">Casual</SelectItem>
                        <SelectItem value="friendly">Friendly</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Enthusiasm Level: {currentConfig.personality.enthusiasm}/10</Label>
                    <Slider
                      value={[currentConfig.personality.enthusiasm]}
                      onValueChange={([value]) => updatePersonality({ enthusiasm: value })}
                      min={1}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Patience Level: {currentConfig.personality.patience}/10</Label>
                    <Slider
                      value={[currentConfig.personality.patience]}
                      onValueChange={([value]) => updatePersonality({ patience: value })}
                      min={1}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
