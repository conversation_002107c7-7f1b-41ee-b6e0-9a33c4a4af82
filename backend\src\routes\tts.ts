import { Router } from 'express';
import { ChatController } from '../controllers/chatController';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * Text-to-Speech Routes
 * Base path: /tts
 */

// All routes require authentication
router.use(authenticate);

// Generate TTS audio with visemes (All authenticated users)
router.post('/', ChatController.generateTTS);

// Get available voice options (All authenticated users)
router.get('/voices', ChatController.getVoiceOptions);

// Get available style options (All authenticated users)
router.get('/styles', ChatController.getStyleOptions);

export default router;
