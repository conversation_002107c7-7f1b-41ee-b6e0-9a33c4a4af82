{"name": "ai-tutor-backend", "version": "1.0.0", "description": "AI-powered tutor platform backend", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "keywords": ["ai", "tutor", "education", "express", "mongodb"], "author": "", "license": "MIT", "dependencies": {"aws-sdk": "^2.1450.0", "bcryptjs": "^2.4.3", "bullmq": "^4.10.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "microsoft-cognitiveservices-speech-sdk": "^1.32.1", "mongoose": "^7.8.7", "multer": "^1.4.5-lts.1", "openai": "^4.0.0", "redis": "^4.6.8", "winston": "^3.10.0", "zod": "^3.22.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/node": "^20.19.11", "jest": "^29.6.2", "nodemon": "^3.1.10", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}