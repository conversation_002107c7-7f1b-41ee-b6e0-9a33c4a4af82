'use client';

// Route guard component for role-based access control

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { UserRole } from '@/types';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  redirectTo?: string;
}

export default function RouteGuard({ 
  children, 
  allowedRoles = [], 
  redirectTo = '/sign-in' 
}: RouteGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push(redirectTo);
      return;
    }

    if (allowedRoles.length > 0 && !allowedRoles.includes(session.user.role)) {
      // Redirect to appropriate dashboard based on user role
      switch (session.user.role) {
        case 'student':
          router.push('/student');
          break;
        case 'teacher':
          router.push('/teacher');
          break;
        case 'hod':
          router.push('/hod');
          break;
        case 'admin':
          router.push('/admin');
          break;
        default:
          router.push('/sign-in');
      }
      return;
    }
  }, [session, status, router, allowedRoles, redirectTo]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(session.user.role)) {
    return null; // Will redirect
  }

  return <>{children}</>;
}
