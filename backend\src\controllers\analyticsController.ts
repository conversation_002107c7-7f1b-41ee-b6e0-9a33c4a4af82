import { Request, Response } from 'express';
import { AnalyticsService } from '../services/analyticsService';
import { AuthenticatedRequest, ApiResponse } from '../types';

export class AnalyticsController {
  /**
   * Get organization analytics
   * GET /analytics/org
   * Frontend: HOD dashboard, organization overview
   */
  static async getOrgAnalytics(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can view organization analytics',
        });
        return;
      }

      // Super admin can specify orgId, HOD uses their own orgId
      const orgId = req.user.role === 'super_admin' && req.query.orgId 
        ? req.query.orgId as string 
        : req.user.orgId;

      const analytics = await AnalyticsService.getOrgAnalytics(orgId);
      
      res.json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get organization analytics';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get global analytics (Super Admin only)
   * GET /analytics/global
   * Frontend: Super Admin dashboard, global overview
   */
  static async getGlobalAnalytics(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'super_admin') {
        res.status(403).json({
          success: false,
          error: 'Only Super Admin can view global analytics',
        });
        return;
      }

      const analytics = await AnalyticsService.getGlobalAnalytics();
      
      res.json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get global analytics';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get user statistics for current organization
   * GET /analytics/users
   * Frontend: User management dashboard
   */
  static async getUserStats(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can view user statistics',
        });
        return;
      }

      // This would be a more detailed user stats endpoint
      // For now, redirect to org analytics
      const orgId = req.user.role === 'super_admin' && req.query.orgId 
        ? req.query.orgId as string 
        : req.user.orgId;

      const analytics = await AnalyticsService.getOrgAnalytics(orgId);
      
      res.json({
        success: true,
        data: {
          users: analytics.users,
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get user statistics';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get material statistics
   * GET /analytics/materials
   * Frontend: Material management dashboard
   */
  static async getMaterialStats(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['teacher', 'hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only Teachers, HOD and Super Admin can view material statistics',
        });
        return;
      }

      const orgId = req.user.role === 'super_admin' && req.query.orgId 
        ? req.query.orgId as string 
        : req.user.orgId;

      const analytics = await AnalyticsService.getOrgAnalytics(orgId);
      
      res.json({
        success: true,
        data: {
          materials: analytics.materials,
          subjects: analytics.subjects,
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get material statistics';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get message/chat statistics
   * GET /analytics/messages
   * Frontend: Chat analytics dashboard
   */
  static async getMessageStats(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['teacher', 'hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only Teachers, HOD and Super Admin can view message statistics',
        });
        return;
      }

      const orgId = req.user.role === 'super_admin' && req.query.orgId 
        ? req.query.orgId as string 
        : req.user.orgId;

      const analytics = await AnalyticsService.getOrgAnalytics(orgId);
      
      res.json({
        success: true,
        data: {
          messages: analytics.messages,
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get message statistics';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get dashboard summary for current user role
   * GET /analytics/dashboard
   * Frontend: Role-specific dashboard
   */
  static async getDashboardSummary(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const analytics = await AnalyticsService.getOrgAnalytics(req.user.orgId);
      
      // Return role-specific summary
      let summary: any = {};

      switch (req.user.role) {
        case 'student':
          summary = {
            availableSubjects: analytics.subjects.total,
            totalMaterials: analytics.materials.total,
            readyMaterials: analytics.materials.byStatus.ready,
          };
          break;

        case 'teacher':
          summary = {
            mySubjects: analytics.subjects.byTeacher.find(t => t.teacherId === req.user!.id)?.subjectCount || 0,
            totalMaterials: analytics.materials.total,
            pendingMessages: analytics.messages.byStatus.pending,
          };
          break;

        case 'hod':
          summary = {
            totalUsers: analytics.users.total,
            totalTeachers: analytics.users.byRole.teacher,
            totalStudents: analytics.users.byRole.student,
            totalSubjects: analytics.subjects.total,
            totalMaterials: analytics.materials.total,
            activeAnnouncements: analytics.announcements.active,
          };
          break;

        case 'super_admin':
          const globalAnalytics = await AnalyticsService.getGlobalAnalytics();
          summary = {
            totalOrganizations: globalAnalytics.organizations.total,
            totalUsers: globalAnalytics.users.total,
            totalSubjects: globalAnalytics.subjects.total,
            totalMaterials: globalAnalytics.materials.total,
          };
          break;
      }

      res.json({
        success: true,
        data: summary,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get dashboard summary';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }
}
