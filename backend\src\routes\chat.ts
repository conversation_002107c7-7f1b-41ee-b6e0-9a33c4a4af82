import { Router } from 'express';
import { Chat<PERSON>ontroller } from '../controllers/chatController';
import { authenticate } from '../middleware/auth';
import { requireStudent, requireTeacher } from '../middleware/rbac';

const router = Router();

/**
 * Chat Routes
 * Base path: /chat
 */

// All routes require authentication
router.use(authenticate);

// Process student question (Students only)
router.post('/', requireStudent, ChatController.processQuestion);

// Get message history (Students only)
router.get('/history', requireStudent, ChatController.getMessageHistory);

// Get pending messages for review (Teachers only)
router.get('/pending', requireTeacher, ChatController.getPendingMessages);

// Review message (Teachers only)
router.put('/messages/:id/review', requireTeacher, ChatController.reviewMessage);

export default router;
