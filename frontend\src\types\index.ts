// Core types for the AI Tutor Platform

export type UserRole = 'student' | 'teacher' | 'hod' | 'admin';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  orgId: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subject {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  teacherName: string;
  orgId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface StudyMaterial {
  id: string;
  title: string;
  description?: string;
  fileUrl: string;
  fileType: 'pdf' | 'video' | 'audio' | 'image' | 'document';
  subjectId: string;
  uploadedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  subjectId?: string;
  hasAudio?: boolean;
  audioUrl?: string;
  furigana?: FuriganaText[];
  grammarBreakdown?: GrammarBreakdown[];
  visemes?: Viseme[];
}

export interface FuriganaText {
  kanji: string;
  reading: string;
  meaning?: string;
}

export interface GrammarBreakdown {
  text: string;
  partOfSpeech: string;
  explanation: string;
  example?: string;
}

export interface Viseme {
  time: number;
  viseme: string;
  value: number;
}

export interface TeacherAvatar {
  id: string;
  teacherId: string;
  modelUrl: string;
  voiceId: string;
  voiceSettings: {
    pitch: number;
    speed: number;
    volume: number;
  };
  appearance: {
    skinTone: string;
    hairColor: string;
    eyeColor: string;
    clothing: string;
  };
  personality: {
    tone: 'formal' | 'casual' | 'friendly' | 'professional';
    enthusiasm: number; // 1-10
    patience: number; // 1-10
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  targetRole?: UserRole;
  orgId: string;
  isUrgent: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Analytics {
  totalUsers: number;
  totalStudents: number;
  totalTeachers: number;
  totalSubjects: number;
  totalMaterials: number;
  totalChats: number;
  activeUsersToday: number;
  activeUsersThisWeek: number;
  popularSubjects: Array<{
    subjectId: string;
    subjectName: string;
    chatCount: number;
  }>;
  userGrowth: Array<{
    date: string;
    count: number;
  }>;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// S3 Upload types
export interface S3SignedUrl {
  url: string;
  key: string;
  fields: Record<string, string>;
}

// Chat streaming types
export interface StreamingResponse {
  type: 'text' | 'audio' | 'visemes' | 'complete';
  content: string;
  audioUrl?: string;
  visemes?: Viseme[];
}

// 3D Scene types
export interface SceneConfig {
  cameraPosition: [number, number, number];
  cameraTarget: [number, number, number];
  lighting: {
    ambient: number;
    directional: {
      intensity: number;
      position: [number, number, number];
    };
  };
  environment: string;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface MaterialUploadForm {
  title: string;
  description?: string;
  subjectId: string;
  file: File;
}

export interface AnnouncementForm {
  title: string;
  content: string;
  targetRole?: UserRole;
  isUrgent: boolean;
}

// Store types
export interface ChatStore {
  messages: ChatMessage[];
  isLoading: boolean;
  isStreaming: boolean;
  currentSubject?: Subject;
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateLastMessage: (updates: Partial<ChatMessage>) => void;
  setLoading: (loading: boolean) => void;
  setStreaming: (streaming: boolean) => void;
  setCurrentSubject: (subject: Subject | undefined) => void;
  clearMessages: () => void;
}

export interface AITeacherStore {
  avatar?: TeacherAvatar;
  isModelLoaded: boolean;
  isSpeaking: boolean;
  currentVisemes: Viseme[];
  blackboardText: string;
  setAvatar: (avatar: TeacherAvatar) => void;
  setModelLoaded: (loaded: boolean) => void;
  setSpeaking: (speaking: boolean) => void;
  setCurrentVisemes: (visemes: Viseme[]) => void;
  setBlackboardText: (text: string) => void;
}

export interface UserStore {
  user?: User;
  subjects: Subject[];
  materials: StudyMaterial[];
  announcements: Announcement[];
  analytics?: Analytics;
  setUser: (user: User | undefined) => void;
  setSubjects: (subjects: Subject[]) => void;
  setMaterials: (materials: StudyMaterial[]) => void;
  setAnnouncements: (announcements: Announcement[]) => void;
  setAnalytics: (analytics: Analytics) => void;
}
