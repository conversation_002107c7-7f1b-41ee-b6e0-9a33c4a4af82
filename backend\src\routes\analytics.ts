import { Router } from 'express';
import { AnalyticsController } from '../controllers/analyticsController';
import { authenticate } from '../middleware/auth';
import { requireHODOrSuperAdmin, requireSuperAdmin, requireTeacherOrHODOrSuperAdmin } from '../middleware/rbac';

const router = Router();

/**
 * Analytics Routes
 * Base path: /analytics
 */

// All routes require authentication
router.use(authenticate);

// Get dashboard summary for current user role (All authenticated users)
router.get('/dashboard', AnalyticsController.getDashboardSummary);

// Get organization analytics (HOD and Super Admin only)
router.get('/org', requireHODOrSuperAdmin, AnalyticsController.getOrgAnalytics);

// Get global analytics (Super Admin only)
router.get('/global', requireSuperAdmin, AnalyticsController.getGlobalAnalytics);

// Get user statistics (HOD and Super Admin only)
router.get('/users', requireHODOrSuperAdmin, AnalyticsController.getUserStats);

// Get material statistics (Teachers, HOD and Super Admin)
router.get('/materials', requireTeacherOrHODOrSuperAdmin, AnalyticsController.getMaterialStats);

// Get message statistics (Teachers, HOD and Super Admin)
router.get('/messages', requireTeacherOrHODOrSuperAdmin, AnalyticsController.getMessageStats);

export default router;
