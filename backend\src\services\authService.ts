import bcrypt from 'bcryptjs';
import { User } from '../models';
import { UserRole } from '../types';
import { generateToken } from '../middleware/auth';

export interface RegisterData {
  email: string;
  password: string;
  role: UserRole;
  orgId: string;
  firstName: string;
  lastName: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export class AuthService {
  static async register(data: RegisterData) {
    const { email, password, role, orgId, firstName, lastName } = data;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new Error('User already exists with this email');
    }

    // Create new user
    const user = new User({
      email,
      passwordHash: password, // Will be hashed by pre-save middleware
      role,
      orgId,
      profile: {
        firstName,
        lastName,
      },
    });

    await user.save();

    // Generate token
    const token = generateToken({
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      orgId: user.orgId,
    });

    return {
      user: user.toJSON(),
      token,
    };
  }

  static async login(data: LoginData) {
    const { email, password } = data;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      throw new Error('Invalid email or password');
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new Error('Invalid email or password');
    }

    // Generate token
    const token = generateToken({
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      orgId: user.orgId,
    });

    return {
      user: user.toJSON(),
      token,
    };
  }

  static async getUserById(userId: string) {
    const user = await User.findById(userId).select('-passwordHash');
    if (!user) {
      throw new Error('User not found');
    }
    return user;
  }

  static async updateUserProfile(userId: string, updates: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
  }) {
    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'profile.firstName': updates.firstName,
          'profile.lastName': updates.lastName,
          'profile.avatar': updates.avatar,
        },
      },
      { new: true, runValidators: true }
    ).select('-passwordHash');

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }
}
