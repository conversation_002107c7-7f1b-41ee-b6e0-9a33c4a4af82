import { Response, NextFunction } from 'express';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';

export const requireRole = (allowedRoles: UserRole | UserRole[]) => {
  return (
    req: AuthenticatedRequest,
    res: Response<ApiResponse>,
    next: NextFunction
  ): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
    
    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Specific role middleware functions
export const requireStudent = requireRole(UserRole.STUDENT);
export const requireTeacher = requireRole(UserRole.TEACHER);
export const requireHOD = requireRole(UserRole.HOD);
export const requireSuperAdmin = requireRole(UserRole.SUPER_ADMIN);

// Combined role middleware
export const requireTeacherOrHOD = requireRole([UserRole.TEACHER, UserRole.HOD]);
export const requireHODOrSuperAdmin = requireRole([UserRole.HOD, UserRole.SUPER_ADMIN]);
export const requireTeacherOrHODOrSuperAdmin = requireRole([
  UserRole.TEACHER,
  UserRole.HOD,
  UserRole.SUPER_ADMIN
]);

// Organization-based access control
export const requireSameOrg = (
  req: AuthenticatedRequest,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  // Super admin can access any organization
  if (req.user.role === UserRole.SUPER_ADMIN) {
    next();
    return;
  }

  // For other roles, check if they belong to the same organization
  const targetOrgId = req.params.orgId || req.body.orgId || req.query.orgId;
  
  if (targetOrgId && targetOrgId !== req.user.orgId) {
    res.status(403).json({
      success: false,
      error: 'Access denied: Different organization'
    });
    return;
  }

  next();
};
