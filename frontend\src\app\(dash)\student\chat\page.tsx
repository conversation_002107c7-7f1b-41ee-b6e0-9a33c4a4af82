'use client';

// Student Chat Page - AI-powered tutoring interface

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageSquare, 
  BookOpen, 
  ArrowLeft,
  Settings,
  Volume2,
  Bot
} from 'lucide-react';
import MessageList from '@/components/chat/MessageList';
import TypingBox from '@/components/chat/TypingBox';
import { useMessages, useIsLoading, useIsStreaming, useCurrentSubject, useChatActions, useChatHelpers } from '@/store/useChat';
import { useSubjects } from '@/store/useUser';
import { mockData } from '@/lib/api';

export default function StudentChatPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const subjectId = searchParams.get('subject');
  
  const messages = useMessages();
  const isLoading = useIsLoading();
  const isStreaming = useIsStreaming();
  const currentSubject = useCurrentSubject();
  const subjects = useSubjects();
  const { setCurrentSubject } = useChatActions();
  const { sendMessage, regenerateLastResponse } = useChatHelpers();
  
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  // Initialize audio element
  useEffect(() => {
    setAudioElement(new Audio());
  }, []);

  // Set current subject based on URL parameter
  useEffect(() => {
    if (subjectId) {
      const subject = subjects.find(s => s.id === subjectId) || 
                     mockData.subjects.find(s => s.id === subjectId);
      if (subject) {
        setCurrentSubject(subject);
      }
    }
  }, [subjectId, subjects, setCurrentSubject]);

  // Handle subject change
  const handleSubjectChange = (newSubjectId: string) => {
    const subject = subjects.find(s => s.id === newSubjectId) || 
                   mockData.subjects.find(s => s.id === newSubjectId);
    if (subject) {
      setCurrentSubject(subject);
      router.push(`/student/chat?subject=${newSubjectId}`);
    }
  };

  // Handle audio playback
  const handlePlayAudio = (audioUrl: string) => {
    if (audioElement) {
      audioElement.src = audioUrl;
      audioElement.play().catch(console.error);
    }
  };

  // Handle sending message
  const handleSendMessage = async (message: string) => {
    await sendMessage(message);
  };

  // Handle regenerating response
  const handleRegenerateResponse = async () => {
    await regenerateLastResponse();
  };

  const availableSubjects = subjects.length > 0 ? subjects : mockData.subjects;

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/student')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-lg font-semibold">AI Tutor Chat</h1>
                {currentSubject && (
                  <p className="text-sm text-gray-600">
                    {currentSubject.name} with {currentSubject.teacherName}
                  </p>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Subject selector */}
            <Select
              value={currentSubject?.id || ''}
              onValueChange={handleSubjectChange}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select a subject" />
              </SelectTrigger>
              <SelectContent>
                {availableSubjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.id}>
                    <div className="flex items-center space-x-2">
                      <BookOpen className="h-4 w-4" />
                      <span>{subject.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Chat settings */}
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </div>

      {/* Subject info banner */}
      {currentSubject && (
        <div className="bg-blue-50 border-b border-blue-200 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Badge variant="secondary">{currentSubject.name}</Badge>
              <span className="text-sm text-gray-600">
                Ask questions, get explanations, and practice with your AI tutor
              </span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MessageSquare className="h-4 w-4" />
              <span>{messages.length} messages</span>
            </div>
          </div>
        </div>
      )}

      {/* No subject selected state */}
      {!currentSubject && (
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-96">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center space-x-2">
                <BookOpen className="h-6 w-6" />
                <span>Select a Subject</span>
              </CardTitle>
              <CardDescription>
                Choose a subject to start chatting with your AI tutor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {availableSubjects.map((subject) => (
                  <Button
                    key={subject.id}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleSubjectChange(subject.id)}
                  >
                    <BookOpen className="h-4 w-4 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">{subject.name}</div>
                      <div className="text-sm text-gray-600">{subject.teacherName}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Chat interface */}
      {currentSubject && (
        <>
          {/* Messages area */}
          <div className="flex-1 overflow-hidden">
            <MessageList
              messages={messages}
              isLoading={isLoading}
              isStreaming={isStreaming}
              onRegenerateResponse={handleRegenerateResponse}
              onPlayAudio={handlePlayAudio}
            />
          </div>

          {/* Input area */}
          <TypingBox
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isStreaming={isStreaming}
            placeholder={`Ask ${currentSubject.teacherName} anything about ${currentSubject.name}...`}
          />
        </>
      )}

      {/* Chat features info */}
      {currentSubject && messages.length === 0 && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center max-w-md">
          <Bot className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome to AI Tutoring!
          </h3>
          <p className="text-gray-600 mb-6">
            I'm your AI tutor for {currentSubject.name}. I can help you with:
          </p>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center space-x-2 text-gray-700">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <span>Explanations</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-700">
              <BookOpen className="h-4 w-4 text-green-600" />
              <span>Grammar help</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-700">
              <Volume2 className="h-4 w-4 text-purple-600" />
              <span>Pronunciation</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-700">
              <Bot className="h-4 w-4 text-orange-600" />
              <span>Practice</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
