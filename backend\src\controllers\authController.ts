import { Request, Response } from 'express';
import { z } from 'zod';
import { AuthService } from '../services/authService';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  role: z.nativeEnum(UserRole),
  orgId: z.string().min(1, 'Organization ID is required'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
});

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  avatar: z.string().url().optional(),
});

export class AuthController {
  /**
   * Register a new user
   * POST /auth/register
   * Frontend: Registration form, admin user creation
   */
  static async register(req: Request, res: Response<ApiResponse>): Promise<void> {
    try {
      const validatedData = registerSchema.parse(req.body);
      
      const result = await AuthService.register(validatedData);
      
      res.status(201).json({
        success: true,
        data: result,
        message: 'User registered successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      const statusCode = errorMessage.includes('already exists') ? 409 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Login user
   * POST /auth/login
   * Frontend: Login form
   */
  static async login(req: Request, res: Response<ApiResponse>): Promise<void> {
    try {
      const validatedData = loginSchema.parse(req.body);
      
      const result = await AuthService.login(validatedData);
      
      res.json({
        success: true,
        data: result,
        message: 'Login successful',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      const statusCode = errorMessage.includes('Invalid') ? 401 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get current user profile
   * GET /auth/me
   * Frontend: User profile display, navigation bar
   */
  static async getMe(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const user = await AuthService.getUserById(req.user.id);
      
      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get user profile';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Update user profile
   * PUT /auth/me
   * Frontend: Profile settings page
   */
  static async updateProfile(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = updateProfileSchema.parse(req.body);
      
      const user = await AuthService.updateUserProfile(req.user.id, validatedData);
      
      res.json({
        success: true,
        data: user,
        message: 'Profile updated successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Logout user (client-side token removal)
   * POST /auth/logout
   * Frontend: Logout button
   */
  static async logout(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    // Since we're using stateless JWT tokens, logout is handled client-side
    // by removing the token from storage
    res.json({
      success: true,
      message: 'Logout successful',
    });
  }
}
