'use client';

// TypingBox component for sending messages with text and voice input

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  Send, 
  Mic, 
  <PERSON>cOff, 
  <PERSON>ader2,
  <PERSON>c<PERSON>,
  <PERSON>
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TypingBoxProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  isStreaming?: boolean;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
}

export default function TypingBox({
  onSendMessage,
  isLoading = false,
  isStreaming = false,
  placeholder = "Type your message here...",
  disabled = false,
  maxLength = 1000
}: TypingBoxProps) {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Handle sending message
  const handleSendMessage = () => {
    if (!message.trim() || isLoading || isStreaming || disabled) return;
    
    onSendMessage(message.trim());
    setMessage('');
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Start voice recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      const audioChunks: Blob[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        console.log('Audio recorded, blob ready for processing:', audioBlob.size, 'bytes');

        // TODO: Send audio to speech-to-text API
        // For now, just add a placeholder message
        const transcribedText = "Voice message transcribed"; // Replace with actual transcription
        setMessage(transcribedText);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };

  // Stop voice recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setRecordingTime(0);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
        recordingIntervalRef.current = null;
      }
    }
  };

  // Format recording time
  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Check if send button should be disabled
  const isSendDisabled = !message.trim() || isLoading || isStreaming || disabled;

  return (
    <div className="border-t bg-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Recording indicator */}
        {isRecording && (
          <div className="mb-3 flex items-center justify-center space-x-2 text-red-600">
            <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">
              Recording... {formatRecordingTime(recordingTime)}
            </span>
          </div>
        )}
        
        <div className="flex items-end space-x-3">
          {/* Voice recording button */}
          <Button
            variant="outline"
            size="icon"
            onClick={isRecording ? stopRecording : startRecording}
            disabled={disabled || isLoading || isStreaming}
            className={cn(
              "flex-shrink-0",
              isRecording && "bg-red-50 border-red-200 text-red-600"
            )}
          >
            {isRecording ? (
              <MicOff className="h-4 w-4" />
            ) : (
              <Mic className="h-4 w-4" />
            )}
          </Button>
          
          {/* Text input area */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={disabled || isRecording}
              maxLength={maxLength}
              className={cn(
                "min-h-[44px] max-h-32 resize-none pr-12",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              )}
              rows={1}
            />
            
            {/* Character count */}
            {message.length > maxLength * 0.8 && (
              <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                {message.length}/{maxLength}
              </div>
            )}
          </div>
          
          {/* Additional action buttons */}
          <div className="flex space-x-2">
            {/* Attachment button (placeholder) */}
            <Button
              variant="outline"
              size="icon"
              disabled={disabled || isLoading || isStreaming}
              className="flex-shrink-0"
              onClick={() => {
                // TODO: Implement file attachment
                console.log('File attachment clicked');
              }}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            
            {/* Emoji button (placeholder) */}
            <Button
              variant="outline"
              size="icon"
              disabled={disabled || isLoading || isStreaming}
              className="flex-shrink-0"
              onClick={() => {
                // TODO: Implement emoji picker
                console.log('Emoji picker clicked');
              }}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Send button */}
          <Button
            onClick={handleSendMessage}
            disabled={isSendDisabled}
            className="flex-shrink-0"
            size="icon"
          >
            {isLoading || isStreaming ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {/* Quick suggestions (optional) */}
        <div className="mt-3 flex flex-wrap gap-2">
          {[
            "Explain this concept",
            "Give me an example",
            "What does this mean?",
            "How do I pronounce this?"
          ].map((suggestion) => (
            <Button
              key={suggestion}
              variant="outline"
              size="sm"
              onClick={() => setMessage(suggestion)}
              disabled={disabled || isLoading || isStreaming || isRecording}
              className="text-xs"
            >
              {suggestion}
            </Button>
          ))}
        </div>
        
        {/* Help text */}
        <div className="mt-2 text-xs text-gray-500 text-center">
          Press Enter to send, Shift+Enter for new line
          {!disabled && " • Click mic to record voice message"}
        </div>
      </div>
    </div>
  );
}
