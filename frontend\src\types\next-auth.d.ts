// NextAuth type extensions

import { UserRole } from './index';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role: UserRole;
      orgId: string;
    };
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role: UserRole;
    orgId: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole;
    orgId: string;
  }
}
