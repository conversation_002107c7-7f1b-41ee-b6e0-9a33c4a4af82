import { Router } from 'express';
import authRoutes from './auth';
import materialRoutes from './materials';
import chatRoutes from './chat';
import ttsRoutes from './tts';
import avatarRoutes from './avatar';
import announcementRoutes from './announcements';
import analyticsRoutes from './analytics';

const router = Router();

/**
 * API Routes
 * All routes are prefixed with /api/v1
 */

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'AI Tutor API is running',
    timestamp: new Date().toISOString(),
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/materials', materialRoutes);
router.use('/chat', chatRoutes);
router.use('/tts', ttsRoutes);
router.use('/avatar', avatarRoutes);
router.use('/announcements', announcementRoutes);
router.use('/analytics', analyticsRoutes);

export default router;
