import mongoose, { Schema } from 'mongoose';
import { ISubject } from '../types';

const subjectSchema = new Schema<ISubject>({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  orgId: {
    type: String,
    required: true,
  },
  teacherId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  description: {
    type: String,
    trim: true,
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
subjectSchema.index({ orgId: 1 });
subjectSchema.index({ teacherId: 1 });
subjectSchema.index({ orgId: 1, teacherId: 1 });

export const Subject = mongoose.model<ISubject>('Subject', subjectSchema);
export default Subject;
