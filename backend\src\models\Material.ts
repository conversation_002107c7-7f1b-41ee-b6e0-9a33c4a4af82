import mongoose, { Schema } from 'mongoose';
import { IMaterial, MaterialStatus } from '../types';

const materialSchema = new Schema<IMaterial>({
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: true,
  },
  teacherId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  s3Key: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: Object.values(MaterialStatus),
    default: MaterialStatus.UPLOADED,
  },
  metadata: {
    fileSize: {
      type: Number,
      required: true,
    },
    mimeType: {
      type: String,
      required: true,
    },
    originalName: {
      type: String,
      required: true,
    },
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
materialSchema.index({ subjectId: 1 });
materialSchema.index({ teacherId: 1 });
materialSchema.index({ status: 1 });
materialSchema.index({ subjectId: 1, status: 1 });

export const Material = mongoose.model<IMaterial>('Material', materialSchema);
export default Material;
