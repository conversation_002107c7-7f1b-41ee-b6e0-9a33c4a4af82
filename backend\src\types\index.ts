import { Request } from 'express';
import { Document, Types } from 'mongoose';

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  HOD = 'hod',
  SUPER_ADMIN = 'super_admin'
}

export enum MaterialStatus {
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error'
}

export enum MessageStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface IUser extends Document {
  email: string;
  passwordHash: string;
  role: UserRole;
  orgId: string;
  profile: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ISubject extends Document {
  name: string;
  orgId: string;
  teacherId: Types.ObjectId;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IMaterial extends Document {
  subjectId: Types.ObjectId;
  teacherId: Types.ObjectId;
  title: string;
  s3Key: string;
  status: MaterialStatus;
  metadata?: {
    fileSize: number;
    mimeType: string;
    originalName: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IChunk extends Document {
  namespace: string;
  text: string;
  embedding: number[];
  metadata: {
    materialId: Types.ObjectId;
    subjectId: Types.ObjectId;
    teacherId: Types.ObjectId;
    chunkIndex: number;
  };
  createdAt: Date;
}

export interface IAvatar extends Document {
  teacherId: Types.ObjectId;
  subjectId: Types.ObjectId;
  style: string;
  voice: string;
  persona?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IMessage extends Document {
  studentId: Types.ObjectId;
  subjectId: Types.ObjectId;
  question: string;
  answer?: string;
  status: MessageStatus;
  reviewedBy?: Types.ObjectId;
  reviewNotes?: string;
  metadata?: {
    retrievedChunks: Types.ObjectId[];
    confidence: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IAnnouncement extends Document {
  orgId: string;
  authorId: Types.ObjectId;
  audience: UserRole[];
  title: string;
  body: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    orgId: string;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
