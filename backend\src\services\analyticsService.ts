import { User, Subject, Material, Message, Announcement } from '../models';
import { UserRole, MaterialStatus, MessageStatus } from '../types';

export interface OrgAnalytics {
  users: {
    total: number;
    byRole: Record<UserRole, number>;
  };
  subjects: {
    total: number;
    byTeacher: Array<{
      teacherId: string;
      teacherName: string;
      subjectCount: number;
    }>;
  };
  materials: {
    total: number;
    byStatus: Record<MaterialStatus, number>;
  };
  messages: {
    total: number;
    byStatus: Record<MessageStatus, number>;
    averageResponseTime: number;
  };
  announcements: {
    total: number;
    active: number;
  };
}

export interface GlobalAnalytics extends OrgAnalytics {
  organizations: {
    total: number;
    topByUsers: Array<{
      orgId: string;
      userCount: number;
    }>;
  };
}

export class AnalyticsService {
  static async getOrgAnalytics(orgId: string): Promise<OrgAnalytics> {
    // Get user statistics
    const userStats = await this.getUserStats(orgId);
    
    // Get subject statistics
    const subjectStats = await this.getSubjectStats(orgId);
    
    // Get material statistics
    const materialStats = await this.getMaterialStats(orgId);
    
    // Get message statistics
    const messageStats = await this.getMessageStats(orgId);
    
    // Get announcement statistics
    const announcementStats = await this.getAnnouncementStats(orgId);

    return {
      users: userStats,
      subjects: subjectStats,
      materials: materialStats,
      messages: messageStats,
      announcements: announcementStats,
    };
  }

  static async getGlobalAnalytics(): Promise<GlobalAnalytics> {
    // Get organization statistics
    const orgStats = await this.getOrgStats();
    
    // Get global user statistics
    const userStats = await this.getUserStats();
    
    // Get global subject statistics
    const subjectStats = await this.getSubjectStats();
    
    // Get global material statistics
    const materialStats = await this.getMaterialStats();
    
    // Get global message statistics
    const messageStats = await this.getMessageStats();
    
    // Get global announcement statistics
    const announcementStats = await this.getAnnouncementStats();

    return {
      organizations: orgStats,
      users: userStats,
      subjects: subjectStats,
      materials: materialStats,
      messages: messageStats,
      announcements: announcementStats,
    };
  }

  private static async getUserStats(orgId?: string) {
    const query = orgId ? { orgId } : {};
    
    const [total, roleStats] = await Promise.all([
      User.countDocuments(query),
      User.aggregate([
        ...(orgId ? [{ $match: { orgId } }] : []),
        {
          $group: {
            _id: '$role',
            count: { $sum: 1 },
          },
        },
      ]),
    ]);

    const byRole = Object.values(UserRole).reduce((acc, role) => {
      acc[role] = 0;
      return acc;
    }, {} as Record<UserRole, number>);

    roleStats.forEach(stat => {
      byRole[stat._id as UserRole] = stat.count;
    });

    return { total, byRole };
  }

  private static async getSubjectStats(orgId?: string) {
    const query = orgId ? { orgId } : {};
    
    const [total, teacherStats] = await Promise.all([
      Subject.countDocuments(query),
      Subject.aggregate([
        ...(orgId ? [{ $match: { orgId } }] : []),
        {
          $group: {
            _id: '$teacherId',
            subjectCount: { $sum: 1 },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'teacher',
          },
        },
        {
          $unwind: '$teacher',
        },
        {
          $project: {
            teacherId: '$_id',
            teacherName: {
              $concat: ['$teacher.profile.firstName', ' ', '$teacher.profile.lastName'],
            },
            subjectCount: 1,
          },
        },
        {
          $sort: { subjectCount: -1 },
        },
        {
          $limit: 10,
        },
      ]),
    ]);

    return {
      total,
      byTeacher: teacherStats,
    };
  }

  private static async getMaterialStats(orgId?: string) {
    let query = {};
    
    if (orgId) {
      // Need to join with subjects to filter by orgId
      const subjects = await Subject.find({ orgId }).select('_id');
      const subjectIds = subjects.map(s => s._id);
      query = { subjectId: { $in: subjectIds } };
    }

    const [total, statusStats] = await Promise.all([
      Material.countDocuments(query),
      Material.aggregate([
        ...(Object.keys(query).length > 0 ? [{ $match: query }] : []),
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
          },
        },
      ]),
    ]);

    const byStatus = Object.values(MaterialStatus).reduce((acc, status) => {
      acc[status] = 0;
      return acc;
    }, {} as Record<MaterialStatus, number>);

    statusStats.forEach(stat => {
      byStatus[stat._id as MaterialStatus] = stat.count;
    });

    return { total, byStatus };
  }

  private static async getMessageStats(orgId?: string) {
    let query = {};
    
    if (orgId) {
      // Need to join with subjects to filter by orgId
      const subjects = await Subject.find({ orgId }).select('_id');
      const subjectIds = subjects.map(s => s._id);
      query = { subjectId: { $in: subjectIds } };
    }

    const [total, statusStats, avgResponseTime] = await Promise.all([
      Message.countDocuments(query),
      Message.aggregate([
        ...(Object.keys(query).length > 0 ? [{ $match: query }] : []),
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
          },
        },
      ]),
      this.calculateAverageResponseTime(query),
    ]);

    const byStatus = Object.values(MessageStatus).reduce((acc, status) => {
      acc[status] = 0;
      return acc;
    }, {} as Record<MessageStatus, number>);

    statusStats.forEach(stat => {
      byStatus[stat._id as MessageStatus] = stat.count;
    });

    return {
      total,
      byStatus,
      averageResponseTime: avgResponseTime,
    };
  }

  private static async getAnnouncementStats(orgId?: string) {
    const query = orgId ? { orgId } : {};
    
    const [total, active] = await Promise.all([
      Announcement.countDocuments(query),
      Announcement.countDocuments({ ...query, isActive: true }),
    ]);

    return { total, active };
  }

  private static async getOrgStats() {
    const [totalOrgs, topOrgs] = await Promise.all([
      User.distinct('orgId').then(orgs => orgs.length),
      User.aggregate([
        {
          $group: {
            _id: '$orgId',
            userCount: { $sum: 1 },
          },
        },
        {
          $sort: { userCount: -1 },
        },
        {
          $limit: 10,
        },
        {
          $project: {
            orgId: '$_id',
            userCount: 1,
            _id: 0,
          },
        },
      ]),
    ]);

    return {
      total: totalOrgs,
      topByUsers: topOrgs,
    };
  }

  private static async calculateAverageResponseTime(query: any): Promise<number> {
    // Mock implementation - in production, this would calculate actual response times
    // based on message creation time and when answers were provided
    return 300; // 5 minutes in seconds
  }
}
