import { Request, Response } from 'express';
import { z } from 'zod';
import { Announcement } from '../models';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';

// Validation schemas
const createAnnouncementSchema = z.object({
  audience: z.array(z.nativeEnum(UserRole)).min(1, 'At least one audience role is required'),
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  body: z.string().min(1, 'Body is required').max(2000, 'Body must be less than 2000 characters'),
});

const updateAnnouncementSchema = z.object({
  audience: z.array(z.nativeEnum(UserRole)).min(1).optional(),
  title: z.string().min(1).max(200).optional(),
  body: z.string().min(1).max(2000).optional(),
  isActive: z.boolean().optional(),
});

export class AnnouncementController {
  /**
   * Create new announcement
   * POST /announcements
   * Frontend: HOD/Super Admin announcement creation form
   */
  static async createAnnouncement(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can create announcements',
        });
        return;
      }

      const validatedData = createAnnouncementSchema.parse(req.body);
      
      const announcement = new Announcement({
        orgId: req.user.orgId,
        authorId: req.user.id,
        audience: validatedData.audience,
        title: validatedData.title,
        body: validatedData.body,
        isActive: true,
      });

      await announcement.save();
      await announcement.populate('authorId', 'profile.firstName profile.lastName email');
      
      res.status(201).json({
        success: true,
        data: announcement,
        message: 'Announcement created successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to create announcement';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get announcements for current user
   * GET /announcements
   * Frontend: Dashboard, notification center
   */
  static async getAnnouncements(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Super admin can see all announcements, others see only their org's announcements
      const orgFilter = req.user.role === 'super_admin' ? {} : { orgId: req.user.orgId };

      const announcements = await Announcement.find({
        ...orgFilter,
        audience: req.user.role,
        isActive: true,
      })
      .populate('authorId', 'profile.firstName profile.lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

      const total = await Announcement.countDocuments({
        ...orgFilter,
        audience: req.user.role,
        isActive: true,
      });

      res.json({
        success: true,
        data: {
          announcements,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get announcements';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get all announcements (for management)
   * GET /announcements/all
   * Frontend: HOD/Super Admin announcement management
   */
  static async getAllAnnouncements(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can view all announcements',
        });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Super admin can see all announcements, HOD sees only their org's announcements
      const orgFilter = req.user.role === 'super_admin' ? {} : { orgId: req.user.orgId };

      const announcements = await Announcement.find(orgFilter)
        .populate('authorId', 'profile.firstName profile.lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Announcement.countDocuments(orgFilter);

      res.json({
        success: true,
        data: {
          announcements,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get announcements';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Update announcement
   * PUT /announcements/:id
   * Frontend: Announcement editing form
   */
  static async updateAnnouncement(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can update announcements',
        });
        return;
      }

      const announcementId = req.params.id;
      const validatedData = updateAnnouncementSchema.parse(req.body);

      // Find announcement and verify access
      const query: any = { _id: announcementId };
      if (req.user.role === 'hod') {
        query.orgId = req.user.orgId;
      }

      const announcement = await Announcement.findOne(query);

      if (!announcement) {
        res.status(404).json({
          success: false,
          error: 'Announcement not found or access denied',
        });
        return;
      }

      // Update fields
      if (validatedData.audience) announcement.audience = validatedData.audience;
      if (validatedData.title) announcement.title = validatedData.title;
      if (validatedData.body) announcement.body = validatedData.body;
      if (validatedData.isActive !== undefined) announcement.isActive = validatedData.isActive;

      await announcement.save();
      await announcement.populate('authorId', 'profile.firstName profile.lastName email');

      res.json({
        success: true,
        data: announcement,
        message: 'Announcement updated successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to update announcement';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Delete announcement
   * DELETE /announcements/:id
   * Frontend: Announcement management page
   */
  static async deleteAnnouncement(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (!['hod', 'super_admin'].includes(req.user.role)) {
        res.status(403).json({
          success: false,
          error: 'Only HOD and Super Admin can delete announcements',
        });
        return;
      }

      const announcementId = req.params.id;

      // Find and delete announcement with access control
      const query: any = { _id: announcementId };
      if (req.user.role === 'hod') {
        query.orgId = req.user.orgId;
      }

      const announcement = await Announcement.findOneAndDelete(query);

      if (!announcement) {
        res.status(404).json({
          success: false,
          error: 'Announcement not found or access denied',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Announcement deleted successfully',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete announcement';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }
}
