# AI Tutor Platform Backend

A production-ready TypeScript backend for an AI-powered educational platform with role-based access control, file management, and intelligent tutoring capabilities.

## 🚀 Features

- **Role-Based Access Control (RBAC)**: Student, Teacher, HOD, Super Admin roles
- **AI-Powered Chat**: RAG-based question answering with OpenAI integration
- **File Management**: S3-compatible storage for study materials
- **Text-to-Speech**: Azure Speech SDK integration with visemes
- **Real-time Analytics**: Organization and global statistics
- **Secure Authentication**: JWT-based auth with bcrypt password hashing
- **Material Processing**: Async job queues for document ingestion and embedding

## 🏗️ Architecture

```
src/
├── config/          # Configuration files (DB, environment)
├── controllers/     # Request handlers
├── middleware/      # Authentication and RBAC middleware
├── models/          # Mongoose schemas
├── routes/          # Express route definitions
├── services/        # Business logic layer
├── types/           # TypeScript type definitions
└── index.ts         # Server entry point
```

## 🛠️ Tech Stack

- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT with bcrypt
- **Validation**: Zod schemas
- **File Storage**: AWS S3
- **AI Integration**: OpenAI API
- **TTS**: Azure Speech SDK
- **Job Queue**: BullMQ with Redis

## 📋 Prerequisites

- Node.js 18+ and npm/yarn
- MongoDB Atlas account
- AWS S3 bucket
- OpenAI API key (optional for development)
- Azure Speech Service key (optional for development)
- Redis instance (for job queues)

## 🚀 Quick Start

1. **Clone and install dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   npm start
   ```

## 🔧 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `MONGODB_URI` | MongoDB connection string | ✅ |
| `JWT_SECRET` | JWT signing secret | ✅ |
| `AWS_ACCESS_KEY_ID` | AWS access key | ✅ |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | ✅ |
| `S3_BUCKET_NAME` | S3 bucket name | ✅ |
| `OPENAI_API_KEY` | OpenAI API key | ⚠️ |
| `AZURE_SPEECH_KEY` | Azure Speech key | ⚠️ |
| `REDIS_URL` | Redis connection URL | ⚠️ |

⚠️ = Optional for development (mock implementations provided)

## 📚 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get current user profile

### Materials Management
- `POST /api/v1/materials` - Create material metadata
- `POST /api/v1/materials/:id/ingest` - Trigger material processing
- `GET /api/v1/materials` - List materials by subject

### AI Chat
- `POST /api/v1/chat` - Process student question
- `GET /api/v1/chat/history` - Get chat history
- `PUT /api/v1/chat/messages/:id/review` - Teacher review

### Text-to-Speech
- `POST /api/v1/tts` - Generate audio with visemes
- `GET /api/v1/tts/voices` - Available voice options

### Avatar Management
- `POST /api/v1/avatar` - Create/update teacher avatar
- `GET /api/v1/avatar/:teacherId` - Get avatar for chat

### Announcements
- `POST /api/v1/announcements` - Create announcement (HOD+)
- `GET /api/v1/announcements` - Get user announcements

### Analytics
- `GET /api/v1/analytics/dashboard` - Role-specific dashboard
- `GET /api/v1/analytics/org` - Organization analytics (HOD+)
- `GET /api/v1/analytics/global` - Global analytics (Super Admin)

## 🔐 Role Permissions

| Role | Permissions |
|------|-------------|
| **Student** | Ask questions, view materials, chat history |
| **Teacher** | Upload materials, create avatars, review answers |
| **HOD** | View org analytics, manage announcements |
| **Super Admin** | Global access, manage all organizations |

## 🏃‍♂️ Development Workflow

1. **Start development server**:
   ```bash
   npm run dev
   ```

2. **Test API endpoints**:
   ```bash
   curl http://localhost:3000/api/v1/health
   ```

3. **Register a test user**:
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "password123",
       "role": "teacher",
       "orgId": "org1",
       "firstName": "John",
       "lastName": "Doe"
     }'
   ```

## 🚀 Production Deployment

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Set production environment variables**

3. **Start with PM2 or similar**:
   ```bash
   pm2 start dist/index.js --name ai-tutor-api
   ```

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run with coverage
npm run test:coverage
```

## 📝 Frontend Integration

### Authentication Flow
```typescript
// Login
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});
const { data } = await response.json();
localStorage.setItem('token', data.token);

// Authenticated requests
const response = await fetch('/api/v1/materials', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### File Upload Flow
```typescript
// 1. Create material metadata
const material = await fetch('/api/v1/materials', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ subjectId, title, fileName, fileSize, mimeType })
});

// 2. Upload file to S3 using signed URL
await fetch(material.uploadUrl, {
  method: 'PUT',
  body: file,
  headers: { 'Content-Type': mimeType }
});

// 3. Trigger processing
await fetch(`/api/v1/materials/${material.id}/ingest`, {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Chat Integration
```typescript
// Send question
const response = await fetch('/api/v1/chat', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ subjectId, question })
});

// Generate TTS for answer
const tts = await fetch('/api/v1/tts', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ text: answer, voice: 'female' })
});
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
