'use client';

// Dashboard layout with navigation and role-based access

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import RouteGuard from '@/components/auth/RouteGuard';
import { Button } from '@/components/ui/button';
import { 
  GraduationCap, 
  LogOut, 
  User, 
  BookOpen, 
  MessageSquare,
  Settings,
  BarChart3,
  Users,
  Bell
} from 'lucide-react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session } = useSession();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/sign-in' });
  };

  const getNavigationItems = () => {
    const role = session?.user?.role;
    
    const baseItems = [
      { icon: BookOpen, label: 'Subjects', href: `/${role}` },
    ];

    switch (role) {
      case 'student':
        return [
          ...baseItems,
          { icon: MessageSquare, label: '<PERSON> Chat', href: '/student/chat' },
        ];
      case 'teacher':
        return [
          ...baseItems,
          { icon: MessageSquare, label: 'AI Chat', href: '/teacher/chat' },
          { icon: Settings, label: 'Avatar Setup', href: '/teacher/avatar' },
        ];
      case 'hod':
        return [
          ...baseItems,
          { icon: Users, label: 'Teachers', href: '/hod/teachers' },
          { icon: BarChart3, label: 'Analytics', href: '/hod/analytics' },
          { icon: Bell, label: 'Announcements', href: '/hod/announcements' },
        ];
      case 'admin':
        return [
          ...baseItems,
          { icon: Users, label: 'Users', href: '/admin/users' },
          { icon: BarChart3, label: 'Analytics', href: '/admin/analytics' },
          { icon: Bell, label: 'Announcements', href: '/admin/announcements' },
          { icon: Settings, label: 'Settings', href: '/admin/settings' },
        ];
      default:
        return baseItems;
    }
  };

  return (
    <RouteGuard allowedRoles={['student', 'teacher', 'hod', 'admin']}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <div className="flex items-center">
                  <GraduationCap className="h-8 w-8 text-blue-600" />
                  <span className="ml-2 text-xl font-bold text-gray-900">
                    AI Tutor Platform
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-gray-500" />
                  <span className="text-sm text-gray-700">
                    {session?.user?.name}
                  </span>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full capitalize">
                    {session?.user?.role}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSignOut}
                  className="flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </Button>
              </div>
            </div>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <nav className="w-64 bg-white shadow-sm min-h-screen">
            <div className="p-4">
              <ul className="space-y-2">
                {getNavigationItems().map((item) => (
                  <li key={item.href}>
                    <button
                      onClick={() => router.push(item.href)}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.label}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </nav>

          {/* Main Content */}
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
      </div>
    </RouteGuard>
  );
}
