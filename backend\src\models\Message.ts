import mongoose, { Schema } from 'mongoose';
import { IMessage, MessageStatus } from '../types';

const messageSchema = new Schema<IMessage>({
  studentId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: true,
  },
  question: {
    type: String,
    required: true,
    trim: true,
  },
  answer: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    enum: Object.values(MessageStatus),
    default: MessageStatus.PENDING,
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  reviewNotes: {
    type: String,
    trim: true,
  },
  metadata: {
    retrievedChunks: [{
      type: Schema.Types.ObjectId,
      ref: 'Chunk',
    }],
    confidence: {
      type: Number,
      min: 0,
      max: 1,
    },
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
messageSchema.index({ studentId: 1 });
messageSchema.index({ subjectId: 1 });
messageSchema.index({ status: 1 });
messageSchema.index({ studentId: 1, createdAt: -1 });
messageSchema.index({ subjectId: 1, status: 1 });

export const Message = mongoose.model<IMessage>('Message', messageSchema);
export default Message;
