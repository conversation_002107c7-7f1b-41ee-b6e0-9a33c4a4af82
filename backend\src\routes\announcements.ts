import { Router } from 'express';
import { AnnouncementController } from '../controllers/announcementController';
import { authenticate } from '../middleware/auth';
import { requireHODOrSuperAdmin } from '../middleware/rbac';

const router = Router();

/**
 * Announcement Routes
 * Base path: /announcements
 */

// All routes require authentication
router.use(authenticate);

// Get announcements for current user (All authenticated users)
router.get('/', AnnouncementController.getAnnouncements);

// Get all announcements for management (HOD and Super Admin only)
router.get('/all', requireHODOrSuperAdmin, AnnouncementController.getAllAnnouncements);

// Create announcement (HOD and Super Admin only)
router.post('/', requireHODOrSuperAdmin, AnnouncementController.createAnnouncement);

// Update announcement (HOD and Super Admin only)
router.put('/:id', requireHODOrSuperAdmin, AnnouncementController.updateAnnouncement);

// Delete announcement (HOD and Super Admin only)
router.delete('/:id', requireHODOrSuperAdmin, AnnouncementController.deleteAnnouncement);

export default router;
