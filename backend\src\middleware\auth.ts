import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';
import { env } from '../config/env';

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Access token required'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const decoded = jwt.verify(token, env.JWT_SECRET) as {
        id: string;
        email: string;
        role: UserRole;
        orgId: string;
      };

      // Verify user still exists
      const user = await User.findById(decoded.id).select('-passwordHash');
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // Attach user info to request
      req.user = {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        orgId: user.orgId
      };

      next();
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
      return;
    }
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};

export const generateToken = (user: {
  id: string;
  email: string;
  role: UserRole;
  orgId: string;
}): string => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    orgId: user.orgId,
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  };

  return jwt.sign(payload, env.JWT_SECRET);
};
