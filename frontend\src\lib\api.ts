// API utility functions for the AI Tutor Platform

import { 
  User, 
  Subject, 
  StudyMaterial, 
  ChatMessage, 
  TeacherAvatar, 
  Announcement, 
  Analytics,
  ApiResponse,
  PaginatedResponse,
  S3SignedUrl,
  StreamingResponse
} from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// Authentication APIs
export const authApi = {
  // Get current user profile
  getMe: (): Promise<ApiResponse<User>> => 
    apiRequest<User>('/me'),

  // Update user profile
  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>
    apiRequest<User>('/me', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
};

// Subject APIs
export const subjectApi = {
  // Get all subjects for current user
  getSubjects: (): Promise<ApiResponse<Subject[]>> =>
    apiRequest<Subject[]>('/subjects'),

  // Get subject by ID
  getSubject: (id: string): Promise<ApiResponse<Subject>> =>
    apiRequest<Subject>(`/subjects/${id}`),

  // Create new subject (teacher/admin only)
  createSubject: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Subject>> =>
    apiRequest<Subject>('/subjects', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update subject (teacher/admin only)
  updateSubject: (id: string, data: Partial<Subject>): Promise<ApiResponse<Subject>> =>
    apiRequest<Subject>(`/subjects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete subject (teacher/admin only)
  deleteSubject: (id: string): Promise<ApiResponse<void>> =>
    apiRequest<void>(`/subjects/${id}`, {
      method: 'DELETE',
    }),
};

// Study Material APIs
export const materialApi = {
  // Get materials for a subject
  getMaterials: (subjectId: string): Promise<ApiResponse<StudyMaterial[]>> =>
    apiRequest<StudyMaterial[]>(`/materials?subjectId=${subjectId}`),

  // Upload new material
  uploadMaterial: (data: FormData): Promise<ApiResponse<StudyMaterial>> =>
    fetch(`${API_BASE_URL}/materials`, {
      method: 'POST',
      body: data,
    }).then(res => res.json()),

  // Delete material
  deleteMaterial: (id: string): Promise<ApiResponse<void>> =>
    apiRequest<void>(`/materials/${id}`, {
      method: 'DELETE',
    }),
};

// Chat APIs
export const chatApi = {
  // Send message and get streaming response
  sendMessage: async function* (
    message: string,
    subjectId?: string
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, subjectId }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }
            try {
              const parsed: StreamingResponse = JSON.parse(data);
              yield parsed;
            } catch (e) {
              console.error('Failed to parse SSE data:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  },

  // Get chat history
  getChatHistory: (subjectId?: string): Promise<ApiResponse<ChatMessage[]>> =>
    apiRequest<ChatMessage[]>(`/chat/history${subjectId ? `?subjectId=${subjectId}` : ''}`),
};

// Text-to-Speech APIs
export const ttsApi = {
  // Get TTS audio and visemes
  getTTS: (messageId: string): Promise<ApiResponse<{ audioUrl: string; visemes: any[] }>> =>
    apiRequest<{ audioUrl: string; visemes: any[] }>(`/tts?id=${messageId}`),
};

// Avatar APIs
export const avatarApi = {
  // Get teacher avatar configuration
  getAvatar: (teacherId?: string): Promise<ApiResponse<TeacherAvatar>> =>
    apiRequest<TeacherAvatar>(`/avatar${teacherId ? `?teacherId=${teacherId}` : ''}`),

  // Save teacher avatar configuration
  saveAvatar: (data: Omit<TeacherAvatar, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<TeacherAvatar>> =>
    apiRequest<TeacherAvatar>('/avatar', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update avatar configuration
  updateAvatar: (id: string, data: Partial<TeacherAvatar>): Promise<ApiResponse<TeacherAvatar>> =>
    apiRequest<TeacherAvatar>(`/avatar/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
};

// S3 Upload APIs
export const s3Api = {
  // Get signed URL for file upload
  getSignedUrl: (fileName: string, fileType: string): Promise<ApiResponse<S3SignedUrl>> =>
    apiRequest<S3SignedUrl>('/s3/sign', {
      method: 'POST',
      body: JSON.stringify({ fileName, fileType }),
    }),

  // Upload file to S3 using signed URL
  uploadFile: async (file: File, signedUrl: S3SignedUrl): Promise<boolean> => {
    try {
      const formData = new FormData();
      Object.entries(signedUrl.fields).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.append('file', file);

      const response = await fetch(signedUrl.url, {
        method: 'POST',
        body: formData,
      });

      return response.ok;
    } catch (error) {
      console.error('S3 upload failed:', error);
      return false;
    }
  },
};

// Announcement APIs
export const announcementApi = {
  // Get announcements
  getAnnouncements: (): Promise<ApiResponse<Announcement[]>> =>
    apiRequest<Announcement[]>('/announcements'),

  // Create announcement (HOD/Admin only)
  createAnnouncement: (data: Omit<Announcement, 'id' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> =>
    apiRequest<Announcement>('/announcements', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Delete announcement
  deleteAnnouncement: (id: string): Promise<ApiResponse<void>> =>
    apiRequest<void>(`/announcements/${id}`, {
      method: 'DELETE',
    }),
};

// Analytics APIs
export const analyticsApi = {
  // Get analytics data (HOD/Admin only)
  getAnalytics: (): Promise<ApiResponse<Analytics>> =>
    apiRequest<Analytics>('/analytics'),

  // Get user analytics
  getUserAnalytics: (userId?: string): Promise<ApiResponse<any>> =>
    apiRequest<any>(`/analytics/users${userId ? `/${userId}` : ''}`),
};

// User Management APIs (Admin only)
export const userApi = {
  // Get all users
  getUsers: (page = 1, limit = 20): Promise<PaginatedResponse<User>> =>
    apiRequest<User[]>(`/users?page=${page}&limit=${limit}`),

  // Create user
  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> =>
    apiRequest<User>('/users', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update user
  updateUser: (id: string, data: Partial<User>): Promise<ApiResponse<User>> =>
    apiRequest<User>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete user
  deleteUser: (id: string): Promise<ApiResponse<void>> =>
    apiRequest<void>(`/users/${id}`, {
      method: 'DELETE',
    }),
};

// Mock data for development (remove when backend is ready)
export const mockData = {
  user: {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'student' as const,
    orgId: 'org1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  subjects: [
    {
      id: '1',
      name: 'Japanese Language',
      description: 'Learn Japanese with AI assistance',
      teacherId: 'teacher1',
      teacherName: 'Tanaka Sensei',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: 'Mathematics',
      description: 'Advanced mathematics concepts',
      teacherId: 'teacher2',
      teacherName: 'Smith Sensei',
      orgId: 'org1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  messages: [
    {
      id: '1',
      content: 'Hello! How can I help you today?',
      role: 'assistant' as const,
      timestamp: new Date(),
      subjectId: '1',
    },
    {
      id: '2',
      content: 'Can you explain the difference between は and が?',
      role: 'user' as const,
      timestamp: new Date(),
      subjectId: '1',
    },
  ],
};
