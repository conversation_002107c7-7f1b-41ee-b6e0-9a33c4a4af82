import { Request, Response } from 'express';
import { z } from 'zod';
import { ChatService } from '../services/chatService';
import { TTSService } from '../services/ttsService';
import { AuthenticatedRequest, ApiResponse } from '../types';

// Validation schemas
const chatSchema = z.object({
  subjectId: z.string().min(1, 'Subject ID is required'),
  question: z.string().min(1, 'Question is required'),
});

const ttsSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  voice: z.string().optional(),
  style: z.string().optional(),
});

const reviewMessageSchema = z.object({
  approved: z.boolean(),
  notes: z.string().optional(),
});

export class ChatController {
  /**
   * Process student question and generate AI response
   * POST /chat
   * Frontend: Chat interface, question submission
   */
  static async processQuestion(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = chatSchema.parse(req.body);
      
      const result = await ChatService.processQuestion({
        ...validatedData,
        studentId: req.user.id,
      });
      
      res.json({
        success: true,
        data: result,
        message: 'Question processed successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to process question';
      const statusCode = errorMessage.includes('not found') ? 404 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Generate text-to-speech audio with visemes
   * POST /tts
   * Frontend: AI avatar animation, audio playback
   */
  static async generateTTS(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const validatedData = ttsSchema.parse(req.body);
      
      const result = await TTSService.generateSpeech(validatedData);
      
      res.json({
        success: true,
        data: result,
        message: 'TTS generated successfully',
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to generate TTS';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get message history for student
   * GET /chat/history?subjectId=...&limit=...
   * Frontend: Chat history sidebar, conversation list
   */
  static async getMessageHistory(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      const subjectId = req.query.subjectId as string;
      const limit = parseInt(req.query.limit as string) || 20;
      
      const messages = await ChatService.getMessageHistory(req.user.id, subjectId, limit);
      
      res.json({
        success: true,
        data: messages,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get message history';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Review and approve/reject AI-generated answer (Teacher only)
   * PUT /chat/messages/:id/review
   * Frontend: Teacher review dashboard, message moderation
   */
  static async reviewMessage(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can review messages',
        });
        return;
      }

      const messageId = req.params.id;
      const validatedData = reviewMessageSchema.parse(req.body);
      
      const message = await ChatService.reviewMessage(
        messageId,
        req.user.id,
        validatedData.approved,
        validatedData.notes
      );
      
      res.json({
        success: true,
        data: message,
        message: `Message ${validatedData.approved ? 'approved' : 'rejected'} successfully`,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          data: error.errors,
        });
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to review message';
      const statusCode = errorMessage.includes('not found') ? 404 : 
                        errorMessage.includes('Access denied') ? 403 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get pending messages for teacher review
   * GET /chat/pending?subjectId=...
   * Frontend: Teacher dashboard, review queue
   */
  static async getPendingMessages(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
        return;
      }

      if (req.user.role !== 'teacher') {
        res.status(403).json({
          success: false,
          error: 'Only teachers can view pending messages',
        });
        return;
      }

      // This would need to be implemented in ChatService
      // For now, return empty array
      res.json({
        success: true,
        data: [],
        message: 'Feature coming soon',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get pending messages';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get available TTS voice options
   * GET /tts/voices
   * Frontend: Voice selection in settings
   */
  static async getVoiceOptions(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const voices = await TTSService.getVoiceOptions();
      
      res.json({
        success: true,
        data: voices,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get voice options';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * Get available TTS style options
   * GET /tts/styles
   * Frontend: Style selection in settings
   */
  static async getStyleOptions(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const styles = await TTSService.getStyleOptions();
      
      res.json({
        success: true,
        data: styles,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get style options';
      
      res.status(500).json({
        success: false,
        error: errorMessage,
      });
    }
  }
}
