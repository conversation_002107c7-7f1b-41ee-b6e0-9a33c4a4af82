import OpenAI from 'openai';
import { Types } from 'mongoose';
import { Chunk, Message, Subject, Avatar } from '../models';
import { MessageStatus } from '../types';
import { env } from '../config/env';

// Initialize OpenAI client (mock for development)
const openai = env.OPENAI_API_KEY && env.OPENAI_API_KEY !== 'mock-openai-key' ? new OpenAI({
  apiKey: env.OPENAI_API_KEY,
}) : null;

export interface ChatRequest {
  studentId: string;
  subjectId: string;
  question: string;
}

export class ChatService {
  static async processQuestion(data: ChatRequest) {
    const { studentId, subjectId, question } = data;

    // Verify subject exists
    const subject = await Subject.findById(subjectId);
    if (!subject) {
      throw new Error('Subject not found');
    }

    // Get teacher's avatar/persona for this subject
    const avatar = await Avatar.findOne({
      teacherId: subject.teacherId,
      subjectId,
    });

    // Retrieve relevant chunks using vector search
    const relevantChunks = await this.retrieveRelevantChunks(question, subjectId);

    // Build context from chunks
    const context = relevantChunks.map(chunk => chunk.text).join('\n\n');

    // Generate AI response
    const answer = await this.generateAnswer(question, context, avatar?.persona);

    // Save message to database
    const message = new Message({
      studentId,
      subjectId,
      question,
      answer,
      status: MessageStatus.PENDING,
      metadata: {
        retrievedChunks: relevantChunks.map(chunk => chunk._id),
        confidence: this.calculateConfidence(relevantChunks),
      },
    });

    await message.save();

    return {
      message,
      chunks: relevantChunks,
    };
  }

  static async retrieveRelevantChunks(question: string, subjectId: string, topK: number = 5) {
    // Mock implementation - in production, this would use vector search
    // For now, we'll do a simple text search
    const chunks = await Chunk.find({
      'metadata.subjectId': subjectId,
      text: { $regex: question.split(' ').join('|'), $options: 'i' }
    })
    .limit(topK)
    .populate('metadata.materialId', 'title');

    return chunks;
  }

  static async generateAnswer(question: string, context: string, persona?: string): Promise<string> {
    // Mock implementation when OpenAI API key is not available
    if (!openai) {
      return `Mock AI Response: Based on the study materials, here's what I found about "${question}". ${context ? 'The relevant context suggests...' : 'However, I need more specific materials to provide a detailed answer.'} This is a mock response for development purposes.`;
    }

    try {
      const systemPrompt = this.buildSystemPrompt(persona);
      const userPrompt = this.buildUserPrompt(question, context);

      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      return completion.choices[0]?.message?.content || 'I apologize, but I could not generate a response at this time.';
    } catch (error) {
      console.error('OpenAI API error:', error);
      return 'I apologize, but I encountered an error while processing your question. Please try again later.';
    }
  }

  static buildSystemPrompt(persona?: string): string {
    const basePrompt = `You are an AI tutor helping students learn. You should:
- Provide clear, educational explanations
- Use the provided context to answer questions accurately
- If the context doesn't contain relevant information, say so politely
- Encourage further learning and ask follow-up questions when appropriate
- Keep responses concise but informative`;

    if (persona) {
      return `${basePrompt}\n\nAdditional persona instructions: ${persona}`;
    }

    return basePrompt;
  }

  static buildUserPrompt(question: string, context: string): string {
    return `Context from study materials:
${context}

Student question: ${question}

Please provide a helpful answer based on the context provided.`;
  }

  static calculateConfidence(chunks: any[]): number {
    // Simple confidence calculation based on number of relevant chunks
    if (chunks.length === 0) return 0;
    if (chunks.length >= 3) return 0.9;
    if (chunks.length >= 2) return 0.7;
    return 0.5;
  }

  static async getMessageHistory(studentId: string, subjectId?: string, limit: number = 20) {
    const query: any = { studentId };
    if (subjectId) {
      query.subjectId = subjectId;
    }

    const messages = await Message.find(query)
      .populate('subjectId', 'name')
      .sort({ createdAt: -1 })
      .limit(limit);

    return messages;
  }

  static async reviewMessage(messageId: string, teacherId: string, approved: boolean, notes?: string) {
    const message = await Message.findById(messageId).populate('subjectId');

    if (!message) {
      throw new Error('Message not found');
    }

    // Verify teacher has access to this subject
    const subject = await Subject.findOne({
      _id: message.subjectId,
      teacherId,
    });

    if (!subject) {
      throw new Error('Access denied: You do not teach this subject');
    }

    // Update message status
    message.status = approved ? MessageStatus.APPROVED : MessageStatus.REJECTED;
    message.reviewedBy = new Types.ObjectId(teacherId);
    message.reviewNotes = notes;

    await message.save();

    return message;
  }
}
