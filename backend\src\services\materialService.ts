import AWS from 'aws-sdk';
import { Material, Subject } from '../models';
import { MaterialStatus } from '../types';
import { env } from '../config/env';

// Configure AWS
AWS.config.update({
  accessKeyId: env.AWS_ACCESS_KEY_ID,
  secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  region: env.AWS_REGION,
});

const s3 = new AWS.S3();

export interface CreateMaterialData {
  subjectId: string;
  teacherId: string;
  title: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

export class MaterialService {
  static async createMaterial(data: CreateMaterialData) {
    const { subjectId, teacherId, title, fileName, fileSize, mimeType } = data;

    // Verify subject exists and teacher has access
    const subject = await Subject.findOne({
      _id: subjectId,
      teacherId,
    });

    if (!subject) {
      throw new Error('Subject not found or access denied');
    }

    // Generate unique S3 key
    const timestamp = Date.now();
    const s3Key = `materials/${subject.orgId}/${subjectId}/${timestamp}-${fileName}`;

    // Create material record
    const material = new Material({
      subjectId,
      teacherId,
      title,
      s3Key,
      status: MaterialStatus.UPLOADED,
      metadata: {
        fileSize,
        mimeType,
        originalName: fileName,
      },
    });

    await material.save();

    // Generate signed URL for upload
    const uploadUrl = await this.generateUploadUrl(s3Key, mimeType);

    return {
      material,
      uploadUrl,
    };
  }

  static async generateUploadUrl(s3Key: string, mimeType: string): Promise<string> {
    const params = {
      Bucket: env.S3_BUCKET_NAME,
      Key: s3Key,
      Expires: 3600, // 1 hour
      ContentType: mimeType,
    };

    return s3.getSignedUrl('putObject', params);
  }

  static async generateDownloadUrl(s3Key: string): Promise<string> {
    const params = {
      Bucket: env.S3_BUCKET_NAME,
      Key: s3Key,
      Expires: 3600, // 1 hour
    };

    return s3.getSignedUrl('getObject', params);
  }

  static async getMaterialsBySubject(subjectId: string, teacherId?: string) {
    const query: any = { subjectId };
    
    // If teacherId is provided, filter by teacher
    if (teacherId) {
      query.teacherId = teacherId;
    }

    const materials = await Material.find(query)
      .populate('teacherId', 'profile.firstName profile.lastName email')
      .sort({ createdAt: -1 });

    return materials;
  }

  static async getMaterialById(materialId: string, teacherId?: string) {
    const query: any = { _id: materialId };
    
    // If teacherId is provided, ensure teacher owns the material
    if (teacherId) {
      query.teacherId = teacherId;
    }

    const material = await Material.findOne(query)
      .populate('teacherId', 'profile.firstName profile.lastName email')
      .populate('subjectId', 'name');

    if (!material) {
      throw new Error('Material not found');
    }

    return material;
  }

  static async updateMaterialStatus(materialId: string, status: MaterialStatus) {
    const material = await Material.findByIdAndUpdate(
      materialId,
      { status },
      { new: true }
    );

    if (!material) {
      throw new Error('Material not found');
    }

    return material;
  }

  static async deleteMaterial(materialId: string, teacherId: string) {
    const material = await Material.findOne({
      _id: materialId,
      teacherId,
    });

    if (!material) {
      throw new Error('Material not found or access denied');
    }

    // Delete from S3
    try {
      await s3.deleteObject({
        Bucket: env.S3_BUCKET_NAME,
        Key: material.s3Key,
      }).promise();
    } catch (error) {
      console.error('Error deleting from S3:', error);
      // Continue with database deletion even if S3 deletion fails
    }

    // Delete from database
    await Material.findByIdAndDelete(materialId);

    return { success: true };
  }

  // Mock implementation for material ingestion
  static async ingestMaterial(materialId: string) {
    // This would typically enqueue a job for processing
    // For now, we'll just update the status
    const material = await this.updateMaterialStatus(materialId, MaterialStatus.PROCESSING);
    
    // TODO: Implement actual ingestion logic with BullMQ
    // - Download file from S3
    // - Extract text content
    // - Chunk the content
    // - Generate embeddings
    // - Store chunks in database
    
    console.log(`Mock: Ingesting material ${materialId}`);
    
    return material;
  }
}
