import mongoose, { Schema } from 'mongoose';
import { IAvatar } from '../types';

const avatarSchema = new Schema<IAvatar>({
  teacherId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  subjectId: {
    type: Schema.Types.ObjectId,
    ref: 'Subject',
    required: true,
  },
  style: {
    type: String,
    required: true,
    enum: ['professional', 'friendly', 'casual', 'formal'],
    default: 'professional',
  },
  voice: {
    type: String,
    required: true,
    enum: ['male', 'female', 'neutral'],
    default: 'neutral',
  },
  persona: {
    type: String,
    trim: true,
    maxlength: 1000,
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
avatarSchema.index({ teacherId: 1 });
avatarSchema.index({ subjectId: 1 });
avatarSchema.index({ teacherId: 1, subjectId: 1 }, { unique: true });

export const Avatar = mongoose.model<IAvatar>('Avatar', avatarSchema);
export default Avatar;
